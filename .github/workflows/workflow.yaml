name: <PERSON><PERSON>da

on:
  push:
    branches: [ "main" ]

jobs:
  linters:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Set PYTHONPATH
        run: echo "PYTHONPATH=$(pwd)" >> $GITHUB_ENV

      - name: Install dev dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements-linters.txt

      - name: Check Lint with flake8
        run: |
          python -m flake8 .

      - name: Black Check
        run: |
          python -m black --check --diff . |
          python -m black .

      - name: Isort Check
        run: |
          python -m isort --check --diff . |
          python -m isort .

  test:
    name: Run tests with PostgreSQL
    runs-on: ubuntu-latest

    services:
      db:
        image: postgres:15
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: password
          POSTGRES_DB: postgres
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    env:
      DATABASE_URL: postgresql://postgres:password@localhost:5432/postgres

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Set PYTHONPATH
        run: echo "PYTHONPATH=$(pwd)" >> $GITHUB_ENV

      - name: Wait for PostgreSQL to be ready
        run: |
          until pg_isready -h localhost -p 5432; do
            echo "Waiting for PostgreSQL..."
            sleep 1
          done

      - name: Install FastApi dependencies
        run: |
          pip install -r requirements-dev.txt

      - name: Coverage Tests
        run: python -m pytest --cov=brain_app --cov-fail-under=85 -x
        env:
          DATABASE_URL: ${{ env.DATABASE_URL }}


  terraform-apply:
    name: 'Terraform Deploy'
    runs-on: ubuntu-latest
    environment: production
    needs: [linters, test]

    defaults:
      run:
        shell: bash
        working-directory: ./infra-lambda

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Terraform Init
        run: terraform init

      - name: Terraform Format
        run: terraform fmt

      - name: Terraform Plan
        env:
          TF_LOG: DEBUG
        run: terraform plan -no-color -input=false -var="database_url=${{ secrets.DATABASE_URL }}"

      - name: Terraform Apply
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
        run: |
          terraform init
          terraform apply -auto-approve -var="database_url=$DATABASE_URL"
