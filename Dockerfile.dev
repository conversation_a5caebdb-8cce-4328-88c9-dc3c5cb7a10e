FROM python:3.11-slim

RUN mkdir -p /home/<USER>

RUN groupadd app && useradd -g app app
RUN usermod -aG app app

ENV APP_HOME=/home/<USER>/src
ENV PYTHONPATH=/home/<USER>/src

RUN mkdir -p $APP_HOME
WORKDIR $APP_HOME

COPY requirements-linters.txt $APP_HOME
COPY requirements-dev.txt $APP_HOME
RUN chown -R app:app /home
USER app
RUN pip install --upgrade pip
RUN pip install -r requirements-dev.txt
RUN pip install -r requirements-linters.txt

ENV PATH="/home/<USER>/.local/bin:${PATH}"
