version: '3.8'

services:
  db:
    container_name: brain-ag-db
    image: postgres:15
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "5437:5432"
    volumes:
      - brain_ag_postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5

  api:
    container_name: brain-ag-api
    build:
      context: .
      dockerfile: Dockerfile.dev
    depends_on:
      - db
    environment:
      - DATABASE_URL=${DATABASE_URL}
    ports:
      - "8000:8000"
    volumes:
      - .:/home/<USER>/src
    tty: true

volumes:
  brain_ag_postgres_data:
